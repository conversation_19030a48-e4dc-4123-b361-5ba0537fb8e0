import SwiftUI
import SwiftData
import Factory
import CrateServices

struct AddTrackToCollectionSheet: View {
  let track: Track
  let onDismiss: () -> Void
  @StateObject private var viewModel: CollectionViewModel
  @Environment(\.dismiss) private var dismiss
  @State private var showingCreateCollectionSheet = false
  @State private var newCollectionName = ""
  @Environment(\.modelContext) private var modelContext

  init(track: Track, onDismiss: @escaping () -> Void) {
    self.track = track
    self.onDismiss = onDismiss
    let collectionService = Container.shared.collectionService.resolve()
    let crateActor = Container.shared.crateActor()
    _viewModel = StateObject(wrappedValue: CollectionViewModel(
      collectionService: collectionService,
      crateActor: crateActor
    ))
  }

  var body: some View {
    NavigationView {
      mainContent
    }
    .onAppear {
      Task {
        await viewModel.loadCollections()
      }
    }
  }

  @ViewBuilder
  private var mainContent: some View {
    VStack(spacing: 0) {
      if viewModel.isLoading {
        loadingView
      } else {
        collectionsList
      }
    }
    .navigationTitle("Add to Collection")
    .navigationBarTitleDisplayMode(.inline)
    .toolbar {
      ToolbarItem(placement: .navigationBarTrailing) {
        Button("Cancel") {
          onDismiss()
          dismiss()
        }
      }
    }
    .sheet(isPresented: $showingCreateCollectionSheet) {
      createCollectionSheet
    }
  }

  @ViewBuilder
  private var loadingView: some View {
    ProgressView()
      .frame(maxWidth: .infinity, maxHeight: .infinity)
  }

  @ViewBuilder
  private var collectionsList: some View {
    List {
      createNewCollectionButton

      if viewModel.collections.isEmpty {
        emptyCollectionsView
      } else {
        ForEach(viewModel.collections, id: \.id) { collection in
          collectionRow(collection)
        }
      }
    }
    .listStyle(InsetGroupedListStyle())
  }

  @ViewBuilder
  private var createNewCollectionButton: some View {
    Button {
      showingCreateCollectionSheet = true
    } label: {
      HStack {
        Image(systemName: "plus.circle.fill")
          .foregroundColor(.blue)
        Text("Create New Collection")
          .foregroundColor(.blue)
      }
    }
    .listRowBackground(Color(UIColor.secondarySystemGroupedBackground))
  }

  @ViewBuilder
  private var emptyCollectionsView: some View {
    Text("No collections available")
      .foregroundColor(.secondary)
      .listRowBackground(Color.clear)
  }

  @ViewBuilder
  private func collectionRow(_ collection: CollectionDTO) -> some View {
    Button {
      Task {
        try? await viewModel.addTrackToCollection(collection, trackToAddDTO: track.toDTO())
        onDismiss()
        dismiss()
      }
    } label: {
      HStack {
        VStack(alignment: .leading, spacing: 4) {
          Text(collection.name ?? "Unnamed Collection")
            .font(.headline)
            .foregroundColor(.primary)

          Text("\(collection.tracks?.count ?? 0) track\(collection.tracks?.count == 1 ? "" : "s")")
            .font(.subheadline)
            .foregroundColor(.secondary)
        }

        Spacer()

        if let tracks = collection.tracks,
           tracks.contains(where: { $0.serverId == track.serverId }) {
          Image(systemName: "checkmark.circle.fill")
            .foregroundColor(.blue)
        }
      }
      .contentShape(Rectangle())
    }
    .buttonStyle(PlainButtonStyle())
  }

  @ViewBuilder
  private var createCollectionSheet: some View {
    NavigationView {
      VStack(spacing: 20) {
        TextField("Collection Name", text: $newCollectionName)
          .padding()
          .background(Color(UIColor.secondarySystemBackground))
          .cornerRadius(8)
          .padding(.horizontal)
          .disabled(viewModel.isLoading)

        if viewModel.isLoading {
          ProgressView("Creating collection...")
        }

        Spacer()
      }
      .padding(.top, 20)
      .navigationTitle("New Collection")
      .navigationBarTitleDisplayMode(.inline)
      .toolbar {
        ToolbarItem(placement: .navigationBarLeading) {
          Button("Cancel") {
            showingCreateCollectionSheet = false
            newCollectionName = ""
          }
          .disabled(viewModel.isLoading)
        }

        ToolbarItem(placement: .navigationBarTrailing) {
          Button("Create") {
            guard !newCollectionName.isEmpty else { return }

            Task {
              do {
                try await viewModel.createCollection(name: newCollectionName)
                if let newCollection = viewModel.collections.last {
                  try await viewModel.addTrackToCollection(newCollection, trackToAddDTO: track.toDTO())
                  newCollectionName = ""
                  showingCreateCollectionSheet = false
                  onDismiss()
                  dismiss()
                }
              } catch {
                print("Error creating collection: \(error)")
              }
            }
          }
          .disabled(newCollectionName.isEmpty || viewModel.isLoading)
        }
      }
    }
  }
}
