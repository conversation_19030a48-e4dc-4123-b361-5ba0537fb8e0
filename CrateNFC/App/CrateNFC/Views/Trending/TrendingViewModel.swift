import CrateServices
import SwiftData
import SwiftUI
import Factory

public enum TrendingViewState {
  case loading
  case error(String)
  case empty
  case content([TrendingTrack])
}

@MainActor
public final class TrendingViewModel: ObservableObject {
  @Published public private(set) var state: TrendingViewState = .empty

  private let apiService: ApiServiceProtocol
  private let trendingTrackService: TrackServiceProtocol

  public init() {
    self.apiService = Container.shared.apiService.resolve()
    self.trendingTrackService = Container.shared.trackService.resolve()
  }

  public func loadCachedTracks() throws {
    if case .content = state {
      return
    }
    updateState(try trendingTrackService.getAllTrendingTracks(context: Container.shared.modelContext.resolve()))
  }

  private func updateState(_ records: [TrendingTrack]) {
    if records.isEmpty {
      state = .empty
    } else {
      state = .content(records)
    }
  }

  public func fetchTrendingTracks() {
    Task { @MainActor in
      do {
        self.state = .loading

        let tracks = try await trendingTrackService.getTrending()
        let models = tracks.map { dto in
          TrendingTrack(
            serverId: dto.serverId,
            name: dto.name,
            artist: dto.artist,
            imgUrl: dto.imgUrl,
            url: dto.url,
            domain: dto.domain,
            updatedAt: dto.updatedAt ?? Date.distantPast,
            createdAt: dto.createdAt ?? Date.distantPast
          )
        }
        self.state = .content(models)

        print("Successfully fetched \(tracks.count) trending tracks")
      } catch {
        print("Error fetching trending tracks: \(error.localizedDescription)")

        do {
          let cachedTracks = try trendingTrackService.getAllTrendingTracks(context: Container.shared.modelContext.resolve())
          if !cachedTracks.isEmpty {
            self.state = .content(cachedTracks)
            return
          }
        } catch {
        }

        self.state = .error(error.localizedDescription)
      }
    }
  }

  func storeFetchedTracks(_ fetchedTracks: [TrendingTrack]) throws {
    try trendingTrackService.deleteAllTrendingTracks(context: Container.shared.modelContext.resolve())
    try trendingTrackService.saveTrendingTracks(fetchedTracks, context: Container.shared.modelContext.resolve())
  }
}
