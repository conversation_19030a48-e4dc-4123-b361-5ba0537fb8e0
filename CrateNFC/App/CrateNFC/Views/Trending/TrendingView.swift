import CrateServices
import SwiftData
import SwiftUI

struct TrendingView: View {
  @Binding private var selectedTab: NavView.Tab
  private var deepLinkHandler: DeepLinkHandler
  @StateObject private var viewModel: TrendingViewModel
  @State private var showingAddToCollectionSheet = false
  @State private var selectedTrack: TrendingTrack?

  public init(selectedTab: Binding<NavView.Tab>, deepLinkHandler: DeepLinkHandler) {
    self._selectedTab = selectedTab
    self._viewModel = StateObject(wrappedValue: TrendingViewModel())
    self.deepLinkHandler = deepLinkHandler
  }

  private func convertToTrack(_ trendingTrack: TrendingTrack) -> Track {
    Track(
      serverId: trendingTrack.serverId,
      name: trendingTrack.name,
      artist: trendingTrack.artist,
      imgUrl: trendingTrack.imgUrl,
      url: trendingTrack.url,
      domain: trendingTrack.domain,
      updatedAt: trendingTrack.updatedAt,
      createdAt: trendingTrack.createdAt
    )
  }

  var body: some View {
    NavigationView {
      ScrollView {
        LazyVStack(spacing: 16) {
          switch viewModel.state {
          case .content(let tracks):
            ForEach(tracks) { track in
              HStack {
                Button(action: { trackLinkClicked(track) }, label: {
                  TrackRow(track: track)
                    .padding(.vertical, 8)
                })

                Spacer()

                // Add + button for adding to collection
                Button(action: {
                  selectedTrack = track
                  showingAddToCollectionSheet = true
                }, label: {
                  Image(systemName: "plus.circle.fill")
                    .foregroundColor(.blue)
                    .font(.title3)
                    .contentShape(Rectangle())
                    .frame(width: 44, height: 44)
                })
                .buttonStyle(BorderlessButtonStyle())
                .padding(.trailing, 8)
              }
              .padding(.horizontal, 16)
              .listRowInsets(EdgeInsets(top: 0, leading: 16, bottom: 0, trailing: 0))
              .listRowBackground(Color.clear)

              Divider()
            }
          case .loading:
            ProgressView()
          case .empty:
            Text("No trending tracks available")
          case .error(let message):
            Text(message)
              .foregroundColor(.red)
          }
        }
        .navigationTitle("Trending Tracks")
        .onAppear {
          try? viewModel.loadCachedTracks()
          if case .empty = viewModel.state {
            viewModel.fetchTrendingTracks()
          }
        }
      }
      .refreshable {
        viewModel.fetchTrendingTracks()
      }
      .sheet(isPresented: $showingAddToCollectionSheet, onDismiss: {
        // Don't reset the track selection immediately on dismiss to avoid issues
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
          selectedTrack = nil
        }
      }, content: {
        // Capture the selected track in a local constant to prevent issues
        if let track = selectedTrack {
          AddTrackToCollectionSheet(
            track: convertToTrack(track),
            onDismiss: { showingAddToCollectionSheet = false }
          )
        } else {
          Text("No track selected")
            .foregroundColor(.secondary)
            .padding()
        }
      })
    }
  }

  private func trackLinkClicked(_ track: TrendingTrack) {
    self.deepLinkHandler.deepLinkURL = URL(string: track.url ?? "")
    self.selectedTab = .write // Switch to Write tab
  }
}
@ViewBuilder
private func getImage(_ imageUrl: URL) -> some View {
  AsyncImage(url: imageUrl) { image in
    image.resizable()
      .aspectRatio(contentMode: .fit)
      .frame(width: 80, height: 80)
      .cornerRadius(8)
  } placeholder: {
    ProgressView()
      .frame(width: 80, height: 80)
  }
}
struct TrackRow: View {
  let track: TrendingTrack

  var body: some View {
    HStack(alignment: .center, spacing: 12) {
      if let imageUrl = track.imgUrl.flatMap({ URL(string: $0) }) {
        AsyncImage(url: imageUrl) { image in
          image.resizable()
            .aspectRatio(contentMode: .fill)
            .frame(width: 60, height: 60)
            .cornerRadius(8)
        } placeholder: {
          ProgressView()
            .frame(width: 60, height: 60)
        }
      } else {
        // Default placeholder for tracks without images
        Image(systemName: "music.note")
          .resizable()
          .aspectRatio(contentMode: .fill)
          .frame(width: 60, height: 60)
          .padding(12)
          .background(Color.gray.opacity(0.2))
          .cornerRadius(8)
      }

      VStack(alignment: .leading, spacing: 4) {
        Text(track.name ?? "Untitled Track")
          .font(.system(size: 16, weight: .semibold))
          .foregroundColor(.blue)
          .lineLimit(1)

        Text(track.artist ?? track.url ?? "-")
          .font(.system(size: 14))
          .foregroundColor(.gray)
          .lineLimit(1)
          .truncationMode(.tail)
      }

      Spacer()
    }
    .frame(maxWidth: .infinity)
    .contentShape(Rectangle())
  }
}

#Preview {
  TrendingView(
    selectedTab: .constant(.trending),
    deepLinkHandler: DeepLinkHandler()
  )
}
