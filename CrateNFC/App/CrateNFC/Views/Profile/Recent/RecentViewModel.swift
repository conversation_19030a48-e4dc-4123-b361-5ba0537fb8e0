import Combine
import CrateServices
import Factory
import SwiftData
import Swift<PERSON>

@MainActor
public final class RecentViewModel: ObservableObject {
  @Published public private(set) var tracks: [TrackDTO] = []
  @Published public private(set) var isLoading = false
  @Published public private(set) var isDeletingTrack = false

  // Services
  private let nfcRecordService: NFCRecordServiceProtocol
  private let trackService: TrackServiceProtocol
  private let userState: UserState
  private var crateActor: CrateActor

  // Cancellables for tracking subscriptions
  private var cancellables = Set<AnyCancellable>()

  // Add state tracking to prevent infinite loops
  private var isRefreshingFromServer = false
  private var lastServerFetchTime: Date?
  private let minimumRefreshInterval: TimeInterval = 5.0 // 5 seconds between refreshes

  public init(crateActor: CrateActor) {
    self.crateActor = crateActor
    nfcRecordService = Container.shared.nfcRecordService.resolve()
    trackService = Container.shared.trackService.resolve()
    userState = Container.shared.userState.resolve()

    // Subscribe to Auth events with receive(on:) to ensure main thread
    AuthPublisher.shared.publisher
      .receive(on: DispatchQueue.main)
      .sink { [weak self] event in
        guard let self = self else { return }

        switch event {
        case .signedIn:
          Task {
            await self.ensureRecentWritesCollectionExists()
            self.fetchTracksFromServer()
          }
        case .signedOut:
          // Clear all local tracks when signing out and ensure UI is updated
          self.tracks = []

          // Then proceed with the database cleanup
          Task {
            await self.clearAllLocalTracks()

            // Prevent automatic reload of tracks for guest users after sign-out
            await MainActor.run {
              self.tracks = [] // Ensure tracks remain empty after cleanup
            }
          }
        case .tokenRefreshed, .profileUpdated, .userCreated:
          break
        }
      }
      .store(in: &cancellables)

    // Initial load based on auth state
    if userState.isSignedIn {
      Task {
        await ensureRecentWritesCollectionExists()
        // If already signed in on init, fetch from server
        fetchTracksFromServer()
      }
    } else {
      // Load local tracks for guest users instead of clearing
      Task {
        await ensureRecentWritesCollectionExists()
        loadLocalTracksForGuest()
      }
    }
  }

  public func updateCrateActor(_ newCrateActor: CrateActor) {
    self.crateActor = newCrateActor
  }

  private func refreshTracks() {
    Task { @MainActor in
      do {
        let recentCollection = try await crateActor.fetchRecentCollection()
        self.tracks = recentCollection?.tracks ?? []
      } catch {
        print("Error refreshing tracks: \(error.localizedDescription)")
      }
    }
  }

  // Ensure the Recent collection exists locally
  private func ensureRecentWritesCollectionExists() async {
    do {
      let recentCollection = try await crateActor.fetchRecentCollection()
      if recentCollection == nil {
        // Create locally if it doesn't exist
        try await crateActor.addRecentCollection(
          name: "Recent",
          thumbnail: "crate_logo",
          tracks: []
        )
      }
    } catch {
      print("Error ensuring 'Recent' collection exists: \(error.localizedDescription)")
    }
  }

  private func updateRecentCollection(_ tracks: [TrackDTO]) async throws {
    try await crateActor.updateRecentCollection(tracks: tracks)
  }

  private func updateViewModelState(_ tracks: [TrackDTO]) {
    self.tracks = tracks.sorted {
      ($0.updatedAt ?? .distantPast) > ($1.updatedAt ?? .distantPast)
    }
    self.lastServerFetchTime = Date()
    self.isRefreshingFromServer = false
    self.isLoading = false
  }

  // Fetch tracks from server - only called on sign in
  private func fetchTracksFromServer() {
    // Prevent concurrent refreshes
    if isRefreshingFromServer {
      return
    }

    isRefreshingFromServer = true
    isLoading = true

    Task {
      do {
        // Fetch fresh tracks from server endpoint
        let fetchedTrackDTOs = try await trackService.getRecent(start: 0, size: 50)

        // Remove duplicates
        let uniqueTracks = removeDuplicateTracks(fetchedTrackDTOs)

        // Update the Recent collection
        try await updateRecentCollection(uniqueTracks)

        // Update the view model's state
        await MainActor.run {
          updateViewModelState(uniqueTracks)
        }
      } catch {
        print("Error fetching tracks from server: \(error.localizedDescription)")
        await MainActor.run {
          self.isRefreshingFromServer = false
          self.isLoading = false
        }
      }
    }
  }

  private func loadLocalTracks() {
    // Skip if we're already refreshing from server
    if isRefreshingFromServer {
      return
    }

    // Skip if user is not authenticated
    if !userState.isSignedIn {
      loadLocalTracksForGuest()
      return
    }

    isLoading = true

    Task {
      do {
        let recentCollection = try await crateActor.fetchRecentCollection()
        if let tracks = recentCollection?.tracks {
          await MainActor.run {
            updateViewModelState(tracks)
          }
        } else {
          await MainActor.run {
            self.tracks = []
            self.isLoading = false
          }
        }
      } catch {
        print("Error loading local tracks: \(error.localizedDescription)")
        await MainActor.run {
          self.tracks = []
          self.isLoading = false
        }
      }
    }
  }

  private func loadLocalTracksForGuest() {
    // Skip if we're already refreshing from server
    if isRefreshingFromServer {
      return
    }

    isLoading = true

    Task {
      do {
        let recentCollection = try await crateActor.fetchRecentCollection()
        await MainActor.run {
          self.tracks = recentCollection?.tracks?.sorted {
            ($0.updatedAt ?? .distantPast) > ($1.updatedAt ?? .distantPast)
          } ?? []
          self.isLoading = false
        }
      } catch {
        await MainActor.run {
          self.tracks = []
          self.isLoading = false
          print("Error fetching local tracks for guest: \(error.localizedDescription)")
        }
      }
    }
  }

  // Public load function now supports both authenticated and guest users
  public func loadTracks() {
    // Skip if we're already refreshing from server
    if isRefreshingFromServer {
      return
    }

    // For inauth users, always load from local collection
    if !userState.isSignedIn {
      loadLocalTracksForGuest()
      return
    }

    // For authenticated users, fetch from server
    fetchTracksFromServer()
  }

  // Delete a single track by id
  public func deleteTrack(_ track: TrackDTO) {
    // Skip if track has no server ID
    guard let serverId = track.serverId else {
      // Just remove it locally
      removeTrackLocally(track)
      return
    }

    isDeletingTrack = true

    // First remove it locally
    removeTrackLocally(track)

    // Then delete from server if signed in
    if userState.isSignedIn {
      Task {
        do {
          _ = try await trackService.deleteTrack(id: serverId)
        } catch {
          print("Error deleting track from server: \(error.localizedDescription)")
        }

        await MainActor.run {
          isDeletingTrack = false
        }
      }
    } else {
      isDeletingTrack = false
    }
  }

  public func removeTrack(at offsets: IndexSet) {
    let tracksToDelete = offsets.map { self.tracks[$0] }

    // Process each track to delete
    for track in tracksToDelete {
      deleteTrack(track)
    }
  }

  public func clearAllTracks() {
    Task {
      do {
        // Clear from server if signed in
        if userState.isSignedIn {
          for track in tracks {
            if let serverId = track.serverId {
              _ = try await trackService.deleteTrack(id: serverId)
            }
          }
        }

        // Clear locally
        try await crateActor.clearRecentCollection()

        await MainActor.run {
          self.tracks = []
        }
      } catch {
        print("Error clearing all tracks: \(error.localizedDescription)")
      }
    }
  }

  // Private helper to remove a track locally
  private func removeTrackLocally(_ track: TrackDTO) {
    Task {
      do {
        // Remove from tracks array
        tracks = tracks.filter { $0.serverId != track.serverId }

        // Remove from Recent collection
        try await crateActor.removeTrackFromRecent(track)
      } catch {
        print("Error deleting track locally: \(error.localizedDescription)")
      }
    }
  }

  private func clearAllLocalTracks() async {
    do {
      try await crateActor.clearRecentCollection()
      await MainActor.run {
        self.tracks = []
      }
    } catch {
      print("Error clearing local tracks: \(error.localizedDescription)")
    }
  }

  // MARK: - Supporting Components

  private func removeDuplicateTracks(_ tracks: [TrackDTO]) -> [TrackDTO] {
    var uniqueTracks: [TrackDTO] = []
    var seenTrackKeys = Set<String>()

    for track in tracks {
      // Normalize the comparison to handle slight variations
      guard let name = track.name?.lowercased().trimmingCharacters(in: .whitespacesAndNewlines),
            let artist = track.artist?.lowercased().trimmingCharacters(in: .whitespacesAndNewlines),
            let domain = track.domain?.lowercased().trimmingCharacters(in: .whitespacesAndNewlines)
      else {
        continue // Skip tracks missing critical information
      }

      let uniqueKey = "\(name)|\(artist)|\(domain)"

      if !seenTrackKeys.contains(uniqueKey) {
        uniqueTracks.append(track)
        seenTrackKeys.insert(uniqueKey)
      }
    }

    return uniqueTracks
  }
}
