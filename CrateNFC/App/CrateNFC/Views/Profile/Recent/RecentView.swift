import CrateServices
import Factory
import SwiftData
import SwiftUI

struct RecentView: View {
  enum DisplayMode {
    case preview
    case fullScreen
  }

  @StateObject private var viewModel: RecentViewModel
  @Binding private var selectedTab: NavView.Tab
  private let displayMode: DisplayMode
  private let deepLinkHandler: DeepLinkHandler
  @Environment(\.dismiss) private var dismiss
  @State private var showingDeleteAllConfirmation = false
  @State private var showingAddToCollectionSheet = false
  @State private var selectedTrack: Track?
  @StateObject private var userState = Container.shared.userState.resolve()
  @Environment(\.modelContext) private var modelContext

  init(
    selectedTab: Binding<NavView.Tab>,
    deepLinkHandler: DeepLinkHandler,
    fullScreen: Bool = false
  ) {
    _selectedTab = selectedTab
    self.deepLinkHandler = deepLinkHandler
    displayMode = fullScreen ? .fullScreen : .preview

    // Create the view model with a temporary CrateActor
    let tempCrateActor = Container.shared.crateActor()
    _viewModel = StateObject(wrappedValue: RecentViewModel(crateActor: tempCrateActor))
  }

  var body: some View {
    Group {
      switch displayMode {
      case .preview:
        previewContent
      case .fullScreen:
        fullScreenContent
      }
    }
    .onAppear {
      // Update the CrateActor with the environment's modelContext when the view appears
      viewModel.updateCrateActor(Container.shared.crateActor())
      viewModel.loadTracks()
    }
    .sheet(isPresented: $showingAddToCollectionSheet, onDismiss: {
      // Don't reset the track selection immediately on dismiss to avoid issues
      DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
        selectedTrack = nil
      }
    }, content: {
      // Capture the selected track in a local constant to prevent issues
      if let track = selectedTrack {
        AddTrackToCollectionSheet(
          track: track,
          onDismiss: { showingAddToCollectionSheet = false }
        )
      } else {
        Text("No track selected")
          .foregroundColor(.secondary)
          .padding()
      }
    })
    .overlay {
      if viewModel.isDeletingTrack {
        Color.black.opacity(0.3)
          .ignoresSafeArea()
          .overlay {
            VStack {
              ProgressView()
                .scaleEffect(1.5)
                .tint(.white)

              Text("Deleting...")
                .foregroundColor(.white)
                .padding(.top, 12)
                .font(.headline)
            }
            .padding(24)
            .background(RoundedRectangle(cornerRadius: 12).fill(Color.gray.opacity(0.7)))
          }
          .transition(.opacity)
      }
    }
    .animation(.easeInOut, value: viewModel.isDeletingTrack)
  }

  // MARK: - Preview Mode Content

  @ViewBuilder
  private var previewContent: some View {
    VStack(alignment: .leading, spacing: 20) {
      // Recent Header
      HStack {
        Text("Recent")
          .font(.title2)
          .fontWeight(.bold)
        Spacer()
        NavigationLink(destination: RecentView(
          selectedTab: $selectedTab,
          deepLinkHandler: deepLinkHandler,
          fullScreen: true
        )) {
          Text("View All")
            .foregroundColor(.blue)
            .fontWeight(.semibold)
        }
      }
      .padding(.horizontal)

      if viewModel.isLoading {
        ProgressView()
          .frame(maxWidth: .infinity)
          .padding()
      } else if viewModel.tracks.isEmpty {
        emptyStateView
      } else {
        ScrollView(.horizontal, showsIndicators: false) {
          HStack(spacing: 12) {
            ForEach(viewModel.tracks.prefix(6)) { track in
              Button(action: {
                trackSelected(track.toModel())
              }, label: {
                NFCRecordRow(record: track.toModel())
              })
            }
          }
          .padding(.horizontal)
        }
      }

      Divider()
        .padding(.vertical, 8)

      // Always embed CollectionView even when there are no tracks
      CollectionView(
        selectedTab: $selectedTab,
        deepLinkHandler: deepLinkHandler,
        collectionService: Container.shared.collectionService.resolve(),
        crateActor: Container.shared.crateActor()
      )
    }
  }

  // MARK: - Empty State View

  private var emptyStateView: some View {
    VStack(spacing: 16) {
      Text("Write an NFC Record to add to this section.")
        .font(.footnote)
        .foregroundColor(.secondary)
        .multilineTextAlignment(.center)
        .padding(.top, 8)
        .padding(.bottom, 4)
      HStack {
        CrateButtonComponent(
          title: "Trending",
          action: { selectedTab = .trending }
        )
        .frame(maxWidth: .infinity)

        CrateButtonComponent(
          title: "Write",
          action: { selectedTab = .write }
        )
        .frame(maxWidth: .infinity)
      }
    }
    .padding(.horizontal)
  }

  // MARK: - Full Screen Mode Content

  private var fullScreenContent: some View {
    List {
      if viewModel.isLoading {
        ProgressView("Loading...")
          .frame(maxWidth: .infinity, alignment: .center)
          .padding()
          .listRowInsets(EdgeInsets())
          .listRowBackground(Color.clear)
      } else if viewModel.tracks.isEmpty {
        emptyStateView
          .frame(maxWidth: .infinity, alignment: .center)
          .padding()
          .listRowInsets(EdgeInsets())
          .listRowBackground(Color.clear)
      } else {
        ForEach(viewModel.tracks) { track in
          HStack {
            Button(action: { trackSelected(track.toModel()) }, label: {
              RecentWritesItemRow(record: track.toModel())
                .padding(.vertical, 8)
            })

            Spacer()

            // Add + button for adding to collection
            Button(action: {
              selectedTrack = track.toModel()
              showingAddToCollectionSheet = true
            }, label: {
              Image(systemName: "plus.circle.fill")
                .foregroundColor(.blue)
                .font(.title3)
                .contentShape(Rectangle())
                .frame(width: 44, height: 44)
            })
            .buttonStyle(BorderlessButtonStyle())
            .padding(.trailing, 8)
          }
          .listRowInsets(EdgeInsets(top: 0, leading: 16, bottom: 0, trailing: 0))
          .listRowBackground(Color.clear)
          .swipeActions(edge: .trailing) {
            Button(role: .destructive) {
              viewModel.deleteTrack(track)
            } label: {
              Label("Delete", systemImage: "trash")
            }
          }
        }
        .onDelete(perform: viewModel.removeTrack)
      }
    }
    .listStyle(.plain)
    .listSectionSeparator(.hidden)
    .navigationTitle("Recent")
    .navigationBarTitleDisplayMode(.large)
    .toolbar {
      ToolbarItem(placement: .navigationBarTrailing) {
        Button(action: {
          showingDeleteAllConfirmation = true
        }, label: {
          Image(systemName: "trash")
            .foregroundColor(.primary)
        })
        .disabled(viewModel.tracks.isEmpty)
      }
    }
    .alert("Delete All Recent", isPresented: $showingDeleteAllConfirmation) {
      Button("Cancel", role: .cancel) {}
      Button("Delete All", role: .destructive) {
        viewModel.clearAllTracks()
      }
    } message: {
      Text("Are you sure you want to delete all recent activity? This will remove all tracks from both your device and the server.")
    }
  }

  // MARK: - Supporting Views

  struct NFCRecordRow: View {
    let record: Track

    var body: some View {
      VStack(alignment: .leading, spacing: 8) {
        if let imageUrl = record.imgUrl.flatMap({ URL(string: $0) }) {
          CachedAsyncImage(url: imageUrl)
        } else {
          Image(systemName: "globe")
            .resizable()
            .scaledToFit()
            .frame(width: 160, height: 160)
            .foregroundColor(.gray)
        }

        VStack(alignment: .leading, spacing: 4) {
          Text(record.name ?? "Web URL")
            .font(.system(size: 16, weight: .semibold))
            .lineLimit(1)
          Text((record.artist ?? record.url) ?? "-")
            .lineLimit(1)
            .truncationMode(.tail)
            .font(.system(size: 14))
            .foregroundColor(.gray)
            .lineLimit(1)
        }
      }
      .frame(width: 160)
    }
  }

  struct RecentWritesItemRow: View {
    let record: Track

    var body: some View {
      HStack(alignment: .center, spacing: 12) {
        if let imageUrl = record.imgUrl.flatMap({ URL(string: $0) }) {
          AsyncImage(url: imageUrl) { image in
            image.resizable()
              .aspectRatio(contentMode: .fill)
              .frame(width: 60, height: 60)
              .cornerRadius(8)
          } placeholder: {
            ProgressView()
              .frame(width: 60, height: 60)
          }
        } else {
          // If no valid URL, display the default placeholder.
          Image(systemName: "globe")
            .resizable()
            .aspectRatio(contentMode: .fill)
            .frame(width: 60, height: 60)
            .foregroundColor(.gray)
        }

        VStack(alignment: .leading, spacing: 4) {
          Text(record.name ?? "Web URL")
            .font(.system(size: 16, weight: .semibold))
            .foregroundColor(.blue)
            .lineLimit(1)
          Text((record.artist ?? record.url) ?? "-")
            .font(.system(size: 14))
            .foregroundColor(.gray)
            .lineLimit(1)
            .truncationMode(.tail)
        }
        Spacer()
      }
      .frame(maxWidth: .infinity)
      .contentShape(Rectangle())
    }
  }

  // MARK: - Helper Methods

  private func trackSelected(_ track: Track) {
    deepLinkHandler.deepLinkURL = URL(string: track.url ?? "")
    selectedTab = .write
  }
}
