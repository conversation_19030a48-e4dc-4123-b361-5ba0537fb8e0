import Combine
import CrateServices
import Factory
import SwiftData
import SwiftUI

public enum ProfileViewState: Equatable {
  case loading
  case error(String)
  case empty
  case content([Track])
}

@MainActor
public final class ProfileViewModel: ObservableObject {
  @Published public private(set) var state: ProfileViewState = .loading
  @Published public var showingSettings = false
  @Published public var showingLogin = false
  @Published public var showingRecentWrites = false

  // Services
  private let userState: UserState
  private let apiService: ApiService
  private let userService: UserServiceProtocol
  private let nfcRecordService: NFCRecordServiceProtocol
  private let modelContext: ModelContext
  public let deepLinkHandler: DeepLinkHandler

  // Cancellables for event subscription
  private var cancellables = Set<AnyCancellable>()

  public var isAuthenticated: Bool {
    return userState.isSignedIn
  }

  public var currentUser: User? {
    return userState.currentUser
  }

  public init(deepLinkHandler: DeepLinkHandler) {
    self.deepLinkHandler = deepLinkHandler
    modelContext = Container.shared.modelContext.resolve()
    nfcRecordService = Container.shared.nfcRecordService.resolve()
    userState = Container.shared.userState.resolve()
    apiService = Container.shared.apiService.resolve()
    userService = Container.shared.userService.resolve()

    // Set up listener for authentication changes using AuthPublisher
    setupAuthChangeListener()

    // Initial load of data if user is signed in
    if userState.isSignedIn {
      refreshProfileData()
    }
  }

  private func setupAuthChangeListener() {
    // Listen to auth events through AuthPublisher
    AuthPublisher.shared.publisher
      .receive(on: DispatchQueue.main)
      .sink { [weak self] event in
        guard let self = self else { return }

        switch event {
        case .signedIn:
          // User signed in - refresh UI state
          self.showingLogin = false
          self.refreshProfileData()

        case .signedOut:
          // Handle sign out
          self.state = .empty
          self.showingLogin = true

        default:
          break
        }
      }
      .store(in: &cancellables)
  }

  // Helper method to refresh all profile data
  // Since this class is marked @MainActor, this function runs on the main thread
  private func refreshProfileData() {
    getNfcRecords()
  }

  // MARK: - NFC Records

  private func updateState(_ records: [Track]) {
    if records.isEmpty {
      state = .empty
    } else {
      state = .content(records)
    }
  }

  public func getNfcRecords() {
    state = .loading

    Task { @MainActor in
      do {
        let fetchedRecords = try nfcRecordService.getAll()
        self.updateState(fetchedRecords)
      } catch {
        self.state = .error("Error fetching NFC records: \(error.localizedDescription)")
      }
    }
  }

  public func logout() {
    userService.logout()
  }
}
