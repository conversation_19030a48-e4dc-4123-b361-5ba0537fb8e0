import CrateServices
import Factory
import SwiftUI

struct ProfileLoginView: View {
  @Environment(\.colorScheme) var colorScheme
  @Environment(\.dismiss) var dismiss
  @StateObject private var viewModel: ProfileLoginViewModel
  @State private var isShowingRegister = false
  @State private var showingConfirmationMessage = false
  @State private var showingPasswordReset = false
  @State private var errorMessage: String = ""
  @State private var isPasswordVisible = false

  public init() {
    _viewModel = StateObject(wrappedValue: ProfileLoginViewModel())
  }

  var body: some View {
    ZStack {
      VStack(spacing: 20) {
        Image("launch")
          .resizable()
          .scaledToFit()
          .frame(width: 150)
          .padding(.top, 10)
          .if(colorScheme == .dark) { view in
            view.colorInvert()
          }

        if showingConfirmationMessage {
          Text("A confirmation email has been sent to your address")
            .font(.headline)
            .multilineTextAlignment(.center)
            .foregroundColor(.green)
            .padding()

          getButton(
            action: {
              showingConfirmationMessage = false
            }, text: "Sign in")
        } else if isShowingRegister {
          ProfileRegisterView(onRegistrationComplete: {
            showingConfirmationMessage = true
            isShowingRegister = false
          })
        } else {
          Text("Login")
            .font(.largeTitle)
            .fontWeight(.bold)
            .foregroundColor(.primary)

          Text("👤 Account Login for Beta Testers coming soon 🕒")
            .foregroundColor(.secondary)
            .font(.footnote)

          getField("Email", $viewModel.email)
          getField("Password", $viewModel.password, isSecure: true, isVisible: $isPasswordVisible)

          if !errorMessage.isEmpty {
            Text(errorMessage)
              .font(.subheadline)
              .foregroundColor(.red)
              .padding(.bottom, 5)
          }

          getButton(
            action: {
              Task {
                do {
                  // Now we can await the login result
                  _ = try await viewModel.handleLogin()
                  await MainActor.run {
                    dismiss()
                  }
                } catch {
                  await MainActor.run {
                    errorMessage = "Invalid email or password"
                  }
                }
              }
            }, text: "Login")

          getButton(action: { showingPasswordReset = true }, text: "Forgot Password?")

          getButton(action: { isShowingRegister = true }, text: "Register")
        }

        Spacer()
      }
      .padding()

      if viewModel.isLoading {
        Color.black.opacity(0.4)
          .ignoresSafeArea()

        ProgressView("Loading...")
          .progressViewStyle(CircularProgressViewStyle(tint: .white))
          .foregroundColor(.white)
          .scaleEffect(1.5)
      }
    }
    .sheet(isPresented: $showingPasswordReset) {
      PasswordResetView()
    }
  }

  @ViewBuilder
  private func getField(
    _ fieldName: String, _ text: Binding<String>, isSecure: Bool = false,
    isVisible: Binding<Bool>? = nil
  )
  -> some View {
    HStack {
      let baseField = Group {
        if isSecure && !(isVisible?.wrappedValue ?? false) {
          SecureField(fieldName, text: text)
        } else {
          TextField(fieldName, text: text)
            .autocapitalization(.none)
        }
      }

      baseField
        .foregroundColor(.primary)
        .autocorrectionDisabled()

      if isSecure, let visibilityBinding = isVisible {
        Button(
          action: {
            visibilityBinding.wrappedValue.toggle()
          },
          label: {
            Image(systemName: visibilityBinding.wrappedValue ? "eye.slash" : "eye")
              .foregroundColor(.secondary)
          }
        )
        .buttonStyle(PlainButtonStyle())
      }
    }
    .padding()
    .cornerRadius(10)
    .overlay(
      RoundedRectangle(cornerRadius: 10)
        .stroke(Color.primary, lineWidth: 1)
    )
  }

  @ViewBuilder
  private func getButton(action: @escaping () -> Void, text: String) -> some View {
    Button(action: action) {
      Text(text)
        .frame(maxWidth: .infinity)
        .padding()
        .background(Color.blue)
        .foregroundColor(.white)
        .cornerRadius(10)
    }
  }
}

#Preview {
  ProfileLoginView()
}
