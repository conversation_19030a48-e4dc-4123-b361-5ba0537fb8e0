import CrateServices
import Factory
import MSAL
import SwiftData
import SwiftUI

@MainActor
public final class ProfileLoginViewModel: ObservableObject {
  @Published public var email: String = ""
  @Published public var password: String = ""
  @Published public var isLoading: Bool = false

  private let HTTPClient: HTTPClient
  private let userService: UserServiceProtocol
  private let userState: UserState
  private let trackService: TrackServiceProtocol
  private let collectionService: CollectionServiceProtocol
  private let modelContext: ModelContext
  var nativeAuth: MSALNativeAuthPublicClientApplication!

  public init(
    userService: UserServiceProtocol? = nil,
    httpClient: HTTPClient? = nil,
    userState: UserState? = nil,
    trackService: TrackServiceProtocol? = nil,
    collectionService: CollectionServiceProtocol? = nil,
    modelContext: ModelContext? = nil
  ) {
    self.userService = userService ?? Container.shared.userService.resolve()
    HTTPClient = httpClient ?? Container.shared.httpClient.resolve()
    self.userState = userState ?? Container.shared.userState.resolve()
    self.trackService = trackService ?? Container.shared.trackService.resolve()
    self.collectionService = collectionService ?? Container.shared.collectionService.resolve()
    self.modelContext = modelContext ?? Container.shared.modelContext.resolve()
  }

  public func handleLogin() async throws -> (User, String) {
    await MainActor.run { isLoading = true }

    do {
      // Use the email and password from the form fields
      let (userDTO, token) = try await userService.login(email: email, password: password)
      let user = userDTO.toModel()
      print("Login successful for user: \(user.email ?? "unknown")")

      await MainActor.run { isLoading = false }  // Set to false on success
      return (user, token)
    } catch {
      await MainActor.run { isLoading = false }  // Set to false on error
      throw error
    }
  }
}
