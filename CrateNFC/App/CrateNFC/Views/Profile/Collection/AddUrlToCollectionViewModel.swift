import CrateServices
import Factory
import Foundation
import SwiftData
import SwiftUI

@MainActor
final class AddUrlToCollectionViewModel: ObservableObject {
  private let trackService: TrackServiceProtocol
  private let collectionService: CollectionServiceProtocol
  private let userState: UserState
  private let modelContext: ModelContext
  private let collection: Collection
  private var debounceWorkItem: DispatchWorkItem?

  enum State {
    case empty
    case fetching
    case loaded(TrackDTO)
    case error
  }

  struct ViewState {
    var enteredURL: String = ""
    var currentURL: String = ""
    var imageURL: URL?
  }

  struct ActiveFlags: OptionSet {
    let rawValue: Int

    static let showEmptyUrlAlert = ActiveFlags(rawValue: 1 << 0)
  }

  @Published var state: State = .empty
  @Published var viewState = ViewState()
  @Published var activeFlags: ActiveFlags = []

  init(modelContext: ModelContext, collection: Collection) {
    self.modelContext = modelContext
    self.collection = collection
    self.trackService = Container.shared.trackService.resolve()
    self.collectionService = Container.shared.collectionService.resolve()
    self.userState = Container.shared.userState.resolve()
  }

  func clearTrackInfo() {
    viewState.enteredURL = ""
    viewState.currentURL = ""
    viewState.imageURL = nil
    state = .empty
  }

  private func convertTrackDTOToModel(_ dto: TrackDTO) -> Track {
    Track(
      serverId: dto.serverId,
      name: dto.name,
      artist: dto.artist,
      imgUrl: dto.imgUrl,
      url: dto.url,
      domain: dto.domain,
      recentCollections: nil,
      collections: [],
      updatedAt: dto.updatedAt ?? Date.distantPast,
      createdAt: dto.createdAt ?? Date.distantPast
    )
  }

  func addTrackToCollection() async throws {
    guard case .loaded(let trackDTO) = state else { return }

    let trackModel = convertTrackDTOToModel(trackDTO)
    collection.tracks.append(trackModel)
    try modelContext.save()
  }

  func handleURLChange() {
    debounceWorkItem?.cancel() // Cancel any existing debounce task

    let workItem = DispatchWorkItem { [weak self] in
      guard let self = self else { return }
      guard !self.viewState.currentURL.isEmpty else {
        self.clearTrackInfo()
        return
      }

      Task {
        self.state = .fetching

        do {
          try await self.trackService.unfurl(url: self.viewState.currentURL)
          if let trackData = try await self.trackService.getSingleRecent() {
            let trackDTO = TrackDTO(
              serverId: trackData.serverId,
              name: trackData.name,
              artist: trackData.artist,
              imgUrl: trackData.imgUrl,
              url: trackData.url,
              domain: trackData.domain,
              recentCollections: nil,
              collections: nil,
              updatedAt: trackData.updatedAt ?? Date.distantPast,
              createdAt: trackData.createdAt ?? Date.distantPast
            )
            self.state = .loaded(trackDTO)
            if let mediaUrl = trackDTO.imgUrl {
              self.viewState.imageURL = URL(string: mediaUrl)
            }
          } else {
            self.state = .error
          }
        } catch {
          print("Error unfurling URL: \(error)")
          self.state = .error
        }
      }
    }

    debounceWorkItem = workItem
    DispatchQueue.main.asyncAfter(deadline: .now() + 0.3, execute: workItem) // 300ms debounce
  }

  func handleAddToCollection() {
    guard case let .loaded(trackDTO) = state else { return }

    Task {
      do {
        let track = trackDTO.toModel()
        try trackService.saveRecentTracks([track], context: modelContext)

        // Add track to collection locally
        collection.tracks.append(track)
        track.collections.append(collection)
        try modelContext.save()

        // Sync with server if user is signed in
        if userState.isSignedIn,
           let collectionServerId = collection.serverId,
           let trackServerId = track.serverId {
          let success = try await collectionService.addTrackToCollection(
            collectionServerId: collectionServerId,
            trackServerId: trackServerId
          )

          if !success {
            // If server sync fails, remove the track locally
            collection.tracks.removeAll(where: { $0 == track })
            track.collections.removeAll(where: { $0 == collection })
            try modelContext.save()
            print("Failed to add track to collection on server")
          }
        }
      } catch {
        print("Error saving track: \(error)")
      }
    }
  }
}
