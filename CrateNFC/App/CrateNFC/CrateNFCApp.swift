import Combine
import CrateServices
import Factory
import Foundation
import MSAL
import SwiftData
import <PERSON><PERSON>

@main
struct CrateNFCApp: App {
  @StateObject private var userState = UserState.shared
  @StateObject var deepLinkHandler = DeepLinkHandler()
  private let container = Container()

  let standardURLs = [
    URL(string: "https://app-cratenfc-testflight-westus.azurewebsites.net")!,
    URL(string: "http://localhost:8000")!,
  ]

  init() {
    if UserDefaults.standard.serverURL == nil {
      print("🚀 First launch: Setting default server URL")
      let defaultURL = standardURLs[0].absoluteString
      UserDefaults.standard.serverURL = defaultURL
    }
    print("📡 Current server URL: \(UserDefaults.standard.serverURL ?? "none")")
    MSALGlobalConfig.loggerConfig.logLevel = .verbose
    MSALGlobalConfig.loggerConfig.setLogCallback { _, message, containsPII in
      if !containsPII {
        print("MSAL: \(message ?? "")")
      }
    }
    // Instantiate the userService on App init so that the user is logged in from *previous cached login.
    _ = container.userService.resolve()

    // Clear collections and tracks on startup
    //        Task {
    //          await CrateNFCApp.clearUserData()
    //        }
  }

  // Static method to clear all user data (collections and tracks)
  private static func clearUserData() async {
    let container = Container()
    let modelContext = container.modelContext.resolve()

    do {
      // Clear Track objects first (since collections may reference tracks)
      let trackDescriptor = FetchDescriptor<Track>()
      let tracks = try modelContext.fetch(trackDescriptor)

      for track in tracks {
        modelContext.delete(track)
      }

      // Clear RecentCollection
      let recentDescriptor = FetchDescriptor<RecentCollection>()
      let recentCollections = try modelContext.fetch(recentDescriptor)

      for collection in recentCollections {
        modelContext.delete(collection)
      }

      // Clear Collection
      let collectionDescriptor = FetchDescriptor<Collection>()
      let collections = try modelContext.fetch(collectionDescriptor)

      for collection in collections {
        modelContext.delete(collection)
      }

      try modelContext.save()
      print(
        "✅ Successfully cleared data: \(tracks.count) Tracks, \(recentCollections.count) RecentCollections, and \(collections.count) Collections"
      )
    } catch {
      print("❌ Error clearing user data: \(error.localizedDescription)")
    }
  }

  var body: some Scene {
    WindowGroup {
      LaunchView()
        .environmentObject(deepLinkHandler)
        .environmentObject(userState)
        .onOpenURL { url in
          deepLinkHandler.handleURL(url)
        }
    }
  }
}

/// Dependency Injection Registry for CrateNFC App.
///
/// This extension defines all service factories for the application using Factory's DI system.
/// Each service is lazily instantiated with controlled lifetime (singleton, cached, etc.)
/// and automatically resolves its dependencies through the container graph.
///
/// Benefits:
/// - Centralizes the creation and wiring of all app dependencies.
/// - Avoids global singletons and tight coupling.
/// - Supports easy mocking and overriding in tests.
/// - Enables clean architecture and testable business logic.
///
/// Usage example:
///     let userService = Container.shared.userService.resolve()
///
/// Service lifetimes:
/// - `singleton`: shared across the entire app lifecycle (e.g. UserState, DeepLinkHandler)
/// - `cached`: reused within the current resolution chain, but can be reset (e.g. ApiService, UserService)
///
/// For more details on Factory, see: https://github.com/hmlongco/Factory

extension Container {
  var userState: Factory<UserState> {
    self { UserState.shared }.singleton
  }

  var httpClient: Factory<HTTPClient> {
    self {
      let defaultURL = "https://app-cratenfc-testflight-westus.azurewebsites.net"
      let serverURL = UserDefaults.standard.serverURL ?? defaultURL
      let client = HTTPClient(baseURL: serverURL)
      if let token = self.userState.resolve().token {
        Task {
          await client.setBearerToken(token)
        }
      }

      // HTTPClient automatically observes server URL changes internally
      return client
    }.singleton
  }

  var modelContext: Factory<ModelContext> {
    self {
      let config = ModelConfiguration(
        schema: Schema([
          TrendingTrack.self, Track.self, Collection.self, User.self, RecentCollection.self,
        ]),
        isStoredInMemoryOnly: false
      )

      do {
        let container = try ModelContainer(
          for: TrendingTrack.self, Track.self, Collection.self, User.self, RecentCollection.self,
          configurations: config
        )
        return ModelContext(container)
      } catch {
        print("💾 SwiftData error: \(error)")

        // Fallback to in-memory storage when persistent storage fails
        let fallbackConfig = ModelConfiguration(isStoredInMemoryOnly: true)
        do {
          let fallbackContainer = try ModelContainer(
            for: TrendingTrack.self, Track.self, Collection.self, User.self, RecentCollection.self,
            configurations: fallbackConfig
          )
          return ModelContext(fallbackContainer)
        } catch {
          fatalError("Failed to create even in-memory ModelContainer: \(error)")
        }
      }
    }.singleton
  }

  var crateActor: Factory<CrateActor> {
    self {
      // Resolve the ModelContainer from the same factory that provides ModelContext
      let modelContainerInstance = self.modelContext().container
      return CrateActor(modelContainer: modelContainerInstance)
    }
    // Choose a scope:
    // .shared // if you want one CrateActor instance reused (careful with @ModelActor state if any besides context)
    // .cached // if different parts of the app might need their "own" instance but it can be reused in a resolution chain
    // .singleton // if you absolutely only want one CrateActor for the entire app.
    // Given @ModelActor has its own context, .cached or .shared is often fine.
    // If it's truly stateless beyond its modelContext, .singleton might even work,
    // but @ModelActors are often instantiated where needed.
    // Let's go with .cached as a reasonable default.
    .cached
  }

  var apiService: Factory<ApiService> {
    self {
      // Note: This creates an ApiService without the API key configured
      // The API key will be set asynchronously when the service is first used
      // For proper initialization, consider using ApiService.create() in async contexts
      let client = self.httpClient.resolve()
      let userState = self.userState.resolve()

      // Set API key asynchronously to avoid blocking DI resolution
      Task {
        await client.setApiKey(UserDefaults.Keys.apiKey)
      }

      return ApiService(client: client, userState: userState)
    }.cached
  }

  var userService: Factory<UserServiceProtocol> {
    self {
      UserService(
        client: self.httpClient.resolve(),
        userState: self.userState.resolve()
      )
    }.cached
  }

  var registrationService: Factory<RegistrationServiceProtocol> {
    self {
      RegistrationService(
        client: self.httpClient.resolve(),
        userState: self.userState.resolve(),
        userService: self.userService.resolve()
      )
    }.cached
  }

  var passwordResetService: Factory<PasswordResetServiceProtocol> {
    self {
      PasswordResetService(
        client: self.httpClient.resolve(),
        userState: self.userState.resolve(),
        userService: self.userService.resolve()
      )
    }.cached
  }

  var collectionService: Factory<CollectionServiceProtocol> {
    self {
      CollectionService(
        client: self.httpClient.resolve(),
        userState: self.userState.resolve()
      )
    }.cached
  }

  var trackService: Factory<TrackServiceProtocol> {
    self {
      TrackService(
        client: self.httpClient.resolve(),
        userState: self.userState.resolve()
      )
    }.cached
  }

  var nfcRecordService: Factory<NFCRecordServiceProtocol> {
    self {
      NFCRecordService(
        context: self.modelContext.resolve()
      )
    }.cached
  }

  var deepLinkHandler: Factory<DeepLinkHandler> {
    self { DeepLinkHandler() }.singleton
  }

  var unfurlService: Factory<UnfurlServiceProtocol> {
    self { UnfurlService() }.cached
  }
}

public class DeepLinkHandler: ObservableObject {
  @Published var deepLinkURL: URL?
  @Published var currentTab: NavView.Tab = .write
  func handleURL(_ url: URL) {
    if let components = URLComponents(url: url, resolvingAgainstBaseURL: false),
      let queryItems = components.queryItems,
      let urlString = queryItems.first(where: { $0.name == "url" })?.value,
      let extractedURL = URL(string: urlString)
    {
      deepLinkURL = extractedURL
      currentTab = .write
    } else {
      deepLinkURL = nil
    }
  }
}
