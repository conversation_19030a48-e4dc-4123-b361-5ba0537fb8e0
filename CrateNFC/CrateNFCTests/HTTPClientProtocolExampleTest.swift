import XCTest
@testable import CrateServices

/// Example test demonstrating how to use HTTPClientProtocol for dependency injection and testing.
/// This shows the proper pattern for testing services that depend on HTTPClient.
final class HTTPClientProtocolExampleTest: XCTestCase {

  private var mockHTTPClient: MockHTTPClient!
  private var apiService: ApiService!

  override func setUp() {
    super.setUp()
    mockHTTPClient = MockHTTPClient()
    // Inject the mock HTTPClient via the protocol
    apiService = ApiService(client: mockHTTPClient, userState: UserState.shared)
  }

  override func tearDown() {
    mockHTTPClient = nil
    apiService = nil
    super.tearDown()
  }

  func testHTTPClientProtocolUsage() async {
    // Given - Set up mock response
    let expectedApiKey = "test-api-key-123"

    // When - Call the service method
    await apiService.updateApiKey(expectedApiKey)

    // Then - Verify the mock captured the expected API key
    XCTAssertEqual(mockHTTPClient.capturedApiKey, expectedApiKey)
  }

  func testMockHTTPClientCapabilities() async throws {
    // Given - Set up a mock response for a GET request
    let mockUserData = """
      {
        "email": "<EMAIL>",
        "username": "testuser"
      }
      """
    mockHTTPClient.mockResponse = mockUserData.data(using: .utf8)

    // When - Make a request through the mock
    struct MockUser: Codable {
      let email: String
      let username: String
    }

    let user: MockUser = try await mockHTTPClient.get(path: "/api/user", parameters: nil as String?)

    // Then - Verify the response was properly decoded
    XCTAssertEqual(user.email, "<EMAIL>")
    XCTAssertEqual(user.username, "testuser")
    XCTAssertEqual(mockHTTPClient.capturedPath, "/api/user")
    XCTAssertEqual(mockHTTPClient.capturedMethod, "GET")
  }

  func testMockHTTPClientErrorHandling() async {
    // Given - Set up a mock error
    mockHTTPClient.setMockError(HTTPError.invalidResponse)

    // When/Then - Verify the error is thrown
    do {
      let _: MockUser = try await mockHTTPClient.get(path: "/api/user", parameters: nil as String?)
      XCTFail("Expected error to be thrown")
    } catch {
      XCTAssertTrue(error is HTTPError)
    }
  }

  func testMockHTTPClientReset() async {
    // Given - Set up some state
    mockHTTPClient.capturedPath = "/test"
    mockHTTPClient.capturedApiKey = "test-key"

    // When - Reset the mock
    mockHTTPClient.reset()

    // Then - Verify all state is cleared
    XCTAssertNil(mockHTTPClient.capturedPath)
    XCTAssertNil(mockHTTPClient.capturedApiKey)
    XCTAssertNil(mockHTTPClient.mockResponse)
    XCTAssertNil(mockHTTPClient.mockError)
  }
}

// MARK: - Helper Types for Testing

private struct MockUser: Codable {
  let email: String
  let username: String
}
