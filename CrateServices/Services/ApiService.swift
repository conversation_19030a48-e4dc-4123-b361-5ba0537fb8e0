import Foundation

public protocol ApiServiceProtocol {
  func updateApiKey(_ key: String) async
}

public struct ApiService: ApiServiceProtocol {
  private let client: HTTPClientProtocol
  private let userState: UserState

  /// Initializer for dependency injection - API key will be set asynchronously
  /// For guaranteed initialization, use `create()` static method instead
  public init(client: HTTPClientProtocol, userState: UserState) {
    self.client = client
    self.userState = userState
  }

  /// Creates a properly initialized ApiService with API key configured
  /// - Parameters:
  ///   - client: The HTTP client to use (defaults to HTTPClient())
  ///   - userState: The user state to use (defaults to UserState.shared)
  /// - Returns: A fully configured ApiService instance
  public static func create(
    client: HTTPClientProtocol = HTTPClient(),
    userState: UserState = UserState.shared
  ) async -> ApiService {
    // Configure the API key before returning the service
    await client.setApiKey(CrateConstants.apiKey)
    return ApiService(client: client, userState: userState)
  }

  public func updateApiKey(_ key: String) async {
    await client.setApiKey(key)
  }
}
