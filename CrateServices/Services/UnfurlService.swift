import Foundation
import SwiftSoup

public protocol UnfurlServiceProtocol {
  func unfurl(url: URL) async throws -> TrackDTO
}

enum UnfurlError: Error, LocalizedError {
  case invalidHTML
  case missingOGTags
  case networkError(Error)
  case other(Error)

  var errorDescription: String? {
    switch self {
    case .invalidHTML:
      return "Could not parse the HTML content."
    case .missingOGTags:
      return "The webpage is missing Open Graph tags."
    case let .networkError(underlyingError):
      return "Network error: \(underlyingError.localizedDescription)"
    case let .other(underlyingError):
      return "An unexpected error occurred: \(underlyingError.localizedDescription)"
    }
  }
}

public class UnfurlService: UnfurlServiceProtocol {
  private let session: URLSession

  public init(session: URLSession = .shared) {
    self.session = session
  }

  private lazy var unfurlHandlers: [String: (URL, Document) async throws -> TrackDTO] = [
    "open.spotify.com": unfurlSpotify,
    "music.apple.com": unfurlAppleMusic,
    "sound.xyz": unfurlSoundXYZ,
    "pandora.com": unfurlPandora,
    "spinamp.xyz": unfurlSpinamp,
    "soundcloud.com": unfurlSoundcloud,
    "newm.io": unfurlNewm,
    "youtube.com": unfurlYoutube,
    "youtu.be": unfurlYoutube
  ]

  public func unfurl(url: URL) async throws -> TrackDTO {
    guard let host = url.host else {
      return try await defaultUnfurl(url: url, doc: Document(""))
    }

    let doc = try await fetchHTML(url: url)

    for (key, handler) in unfurlHandlers where host.contains(key) {
      return try await handler(url, doc)
    }

    return try await defaultUnfurl(url: url, doc: doc)
  }

  func fetchHTML(url: URL) async throws -> Document {
    let htmlString = try await Task.detached {
      do {
        // Use the injected session instead of URLSession.shared
        let (data, _) = try await self.session.data(from: url)
        guard let html = String(data: data, encoding: .utf8) else {
          throw UnfurlError.invalidHTML
        }
        return html
      } catch {
        throw UnfurlError.networkError(error)
      }
    }.value

    return try SwiftSoup.parse(htmlString)
  }

  private func extractOGMetadata(from doc: Document) throws -> [String: String] {
    var ogData: [String: String] = [:]

    if let metaTags = try doc.head()?.select("meta[property^=og:]") {
      for meta in metaTags {
        let property = try meta.attr("property")
        let content = try meta.attr("content")
        ogData[property] = content
      }
    }

    if ogData.isEmpty {
      throw UnfurlError.missingOGTags
    }
    return ogData
  }

  private func unfurlSpotify(url: URL, doc: Document) async throws -> TrackDTO {
    let ogData = try extractOGMetadata(from: doc)

    let trackName = ogData["og:title"]
    let artist = ogData["og:description"]?.components(separatedBy: "·").first?.trimmingCharacters(in: .whitespaces)

    return TrackDTO(
      name: trackName,
      artist: artist,
      imgUrl: ogData["og:image"],
      url: url.absoluteString,
      domain: url.host,
      updatedAt: Date(),
      createdAt: Date()
    )
  }

  private func unfurlAppleMusic(url: URL, doc: Document) async throws -> TrackDTO {
    let ogData = try extractOGMetadata(from: doc)

    guard let fullTitle = ogData["og:title"] else {
      throw UnfurlError.missingOGTags
    }

    let components = fullTitle.components(separatedBy: " by ")
    let trackName = components.first?.trimmingCharacters(in: .whitespacesAndNewlines)
    let artist = components.last?.components(separatedBy: " on ").first?.trimmingCharacters(in: .whitespacesAndNewlines)

    var imgUrl: String?

    if let twitterImageTag = try doc.select("meta[property=twitter:image]").first() {
      imgUrl = try twitterImageTag.attr("content")
    }

    if imgUrl == nil, let twitterImageTag = try doc.select("meta[name=twitter:image]").first() {
      imgUrl = try twitterImageTag.attr("content")
    }

    if imgUrl == nil {
      imgUrl = ogData["og:image"]
    }

    return TrackDTO(
      name: trackName,
      artist: artist,
      imgUrl: imgUrl,
      url: url.absoluteString,
      domain: url.host,
      updatedAt: Date(),
      createdAt: Date()
    )
  }

  private func unfurlSoundXYZ(url: URL, doc: Document) async throws -> TrackDTO {
    let ogData = try extractOGMetadata(from: doc)

    let title = ogData["og:title"]
    var artist: String?
    var trackName: String?

    if let title = title {
      let components = title.components(separatedBy: " - ")
      if components.count >= 2 {
        artist = components[0]
        trackName = components[1]
      }
    }

    let imgUrl = ogData["og:image"]

    return TrackDTO(
      name: trackName,
      artist: artist,
      imgUrl: imgUrl,
      url: url.absoluteString,
      domain: url.host,
      updatedAt: Date(),
      createdAt: Date()
    )
  }

  private func unfurlPandora(url: URL, doc: Document) async throws -> TrackDTO {
    let ogData = try extractOGMetadata(from: doc)

    let trackName = ogData["og:title"]
    var artist: String?

    if let description = try doc.select("meta[name=description]").first()?.attr("content") {
      if let listenRange = description.range(of: "Listen to "),
         let pandoraRange = description.range(of: " on Pandora") {
        let artistStartIndex = listenRange.upperBound
        let artistEndIndex = pandoraRange.lowerBound
        artist = String(description[artistStartIndex ..< artistEndIndex])
      }
    }

    return TrackDTO(
      name: trackName,
      artist: artist,
      imgUrl: ogData["og:image"],
      url: url.absoluteString,
      domain: url.host,
      updatedAt: Date(),
      createdAt: Date()
    )
  }

  private func unfurlSpinamp(url: URL, doc: Document) async throws -> TrackDTO {
    // Extract title from URL
    let path = url.path

    let regex = try NSRegularExpression(pattern: "/track/([^/]+)-\\d+", options: [])
    let nsString = path as NSString
    let results = regex.matches(in: path, options: [], range: NSRange(location: 0, length: nsString.length))

    var title: String?
    if let match = results.first {
      let titleRange = match.range(at: 1)
      title = nsString.substring(with: titleRange).replacingOccurrences(of: "-", with: " ").capitalized
    }

    // Because the raw HTML doesn't contain artist info, we hardcode it
    let artist = "Spinamp"

    // Extract og:image using the helper function
    let ogData = try extractOGMetadata(from: doc)
    let imgUrl = ogData["og:image"]

    return TrackDTO(
      name: title,
      artist: artist,
      imgUrl: imgUrl,
      url: url.absoluteString,
      domain: url.host,
      updatedAt: Date(),
      createdAt: Date()
    )
  }

  private func unfurlSoundcloud(url: URL, doc: Document) async throws -> TrackDTO {
    let ogData = try extractOGMetadata(from: doc)

    let trackName = ogData["og:title"]
    var artist: String?

    if let description = try doc.select("meta[name=description]").first()?.attr("content") {
      if let byRange = description.range(of: "by "),
         let onRange = description.range(of: " on") {
        let artistStartIndex = byRange.upperBound
        let artistEndIndex = onRange.lowerBound
        artist = String(description[artistStartIndex ..< artistEndIndex])
      }
    }

    return TrackDTO(
      name: trackName,
      artist: artist,
      imgUrl: ogData["og:image"],
      url: url.absoluteString,
      domain: url.host,
      updatedAt: Date(),
      createdAt: Date()
    )
  }

  private func unfurlNewm(url: URL, doc: Document) async throws -> TrackDTO {
    // Implement Newm-specific unfurling logic here
    // ...
    return try await defaultUnfurl(url: url, doc: doc) // placeholder
  }

  private func unfurlYoutube(url: URL, doc: Document) async throws -> TrackDTO {
    // Get the raw HTML content -- Nope! YouTube doesn't allow this.
    let rawHTML = try doc.html()
    print("Raw HTML for YouTube URL: \(rawHTML)")

    _ = try extractOGMetadata(from: doc)

    var trackName: String?
    var artist: String?

    // we never get this far because of youtube...
    if let title = try doc.select("meta[name=title]").first()?.attr("content") {
      let components = title.components(separatedBy: " - ")
      if components.count >= 2 {
        artist = components[0]
        trackName = components[1]
      }
    }

    // hardcode because we can't get anything from YouTube...
    let imgUrl = "https://www.youtube.com/img/desktop/yt_1200.png"

    return TrackDTO(
      name: trackName,
      artist: artist,
      imgUrl: imgUrl,
      url: url.absoluteString,
      domain: url.host,
      updatedAt: Date(),
      createdAt: Date()
    )
  }

  private func defaultUnfurl(url: URL, doc: Document) async throws -> TrackDTO {
    let ogData = try extractOGMetadata(from: doc)

    return TrackDTO(
      name: ogData["og:title"],
      artist: ogData["og:description"],
      imgUrl: ogData["og:image"],
      url: url.absoluteString,
      domain: url.host,
      updatedAt: Date(),
      createdAt: Date()
    )
  }
}
