import Foundation
import SwiftData

public protocol NFCRecordServiceProtocol {
  func getAll() throws -> [Track]
  func save(_ record: Track) throws
  func deleteAll() throws
}

public struct NFCRecordService: NFCRecordServiceProtocol {
  private let context: ModelContext

  public init(context: ModelContext) {
    self.context = context
  }

  // Temporary code until we find a better solution
  public func getAll() throws -> [Track] {
    var fetchDescriptor = FetchDescriptor<Track>()
    fetchDescriptor.sortBy = [SortDescriptor(\Track.createdAt, order: .reverse)]
    fetchDescriptor.fetchLimit = 20

    let existingRecords = try context.fetch(fetchDescriptor)

    let uniqueRecords = existingRecords.uniqued(on: { record in
      record.url ?? ""
    })
    return Array(uniqueRecords)
  }

  public func save(_ record: Track) throws {
    context.insert(record)
    try context.save()
  }

  public func deleteAll() throws {
    try context.delete(model: Track.self)
    try context.save()
  }
}
