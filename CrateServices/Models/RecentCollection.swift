import Foundation
import SwiftData

@Model
public final class RecentCollection {
  public var name: String?
  public var thumbnail: String?
  public var tracks: [Track]
  public var createdAt: Date?
  public var updatedAt: Date?

  public init(
    name: String?,
    tracks: [Track] = [],
    thumbnail: String? = nil,
    createdAt: Date? = nil,
    updatedAt: Date? = nil
  ) {
    self.name = name
    self.tracks = tracks
    self.thumbnail = thumbnail
    self.createdAt = createdAt
    self.updatedAt = updatedAt
  }
}

public final class RecentCollectionDTO: Sendable, Identifiable {
  public let name: String?
  public let thumbnail: String?
  public let tracks: [TrackDTO]?
  public let createdAt: Date?
  public let updatedAt: Date?

  public init(
    name: String? = nil,
    thumbnail: String? = nil,
    tracks: [TrackDTO]? = nil,
    createdAt: Date? = nil,
    updatedAt: Date? = nil
  ) {
    self.name = name
    self.thumbnail = thumbnail
    self.tracks = tracks
    self.createdAt = createdAt
    self.updatedAt = updatedAt
  }
}
