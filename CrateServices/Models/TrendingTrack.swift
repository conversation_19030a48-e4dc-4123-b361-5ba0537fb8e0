import Foundation
import SwiftData

@Model
public final class TrendingTrack {
  public var serverId: Int?
  public var name: String?
  public var artist: String?
  public var imgUrl: String?
  public var url: String?
  public var domain: String?
  public var updatedAt: Date?
  public var createdAt: Date?
  public init(
    serverId: Int? = nil,
    name: String? = nil,
    artist: String? = nil,
    imgUrl: String? = nil,
    url: String? = nil,
    domain: String? = nil,
    updatedAt: Date = Date(),
    createdAt: Date = Date()
  ) {
    self.serverId = serverId
    self.name = name
    self.artist = artist
    self.imgUrl = imgUrl
    self.url = url
    self.domain = domain
    self.updatedAt = updatedAt
    self.createdAt = createdAt
  }
}

public final class TrendingTrackDTO: Sendable, Identifiable {
  public let serverId: Int?
  public let name: String?
  public let artist: String?
  public let imgUrl: String?
  public let url: String?
  public let domain: String?
  public let updatedAt: Date?
  public let createdAt: Date?

  public init(
    serverId: Int? = nil,
    name: String? = nil,
    artist: String? = nil,
    imgUrl: String? = nil,
    url: String? = nil,
    domain: String? = nil,
    updatedAt: Date? = nil,
    createdAt: Date? = nil
  ) {
    self.serverId = serverId
    self.name = name
    self.artist = artist
    self.imgUrl = imgUrl
    self.url = url
    self.domain = domain
    self.updatedAt = updatedAt
    self.createdAt = createdAt
  }
}
