import Foundation
import SwiftData

@Model
public final class Collection {
  public var serverId: Int?
  public var name: String?
  public var thumbnail: String?
  @Relationship(inverse: \Track.collections) public var tracks: [Track]
  public var createdAt: Date?
  public var updatedAt: Date?

  public init(
    serverId: Int? = nil,
    name: String?,
    tracks: [Track] = [],
    thumbnail: String? = nil,
    createdAt: Date? = nil,
    updatedAt: Date? = nil
  ) {
    self.serverId = serverId
    self.name = name
    self.tracks = tracks
    self.thumbnail = thumbnail
    self.createdAt = createdAt
    self.updatedAt = updatedAt
  }

  public func toDTO() -> CollectionDTO {
    CollectionDTO(
      serverId: serverId,
      name: name,
      thumbnail: thumbnail,
      tracks: tracks.map { track in
        TrackDTO(
          serverId: track.serverId,
          name: track.name,
          artist: track.artist,
          imgUrl: track.imgUrl,
          url: track.url,
          domain: track.domain,
          recentCollections: nil,
          collections: nil,
          updatedAt: track.updatedAt,
          createdAt: track.createdAt
        )
      },
      createdAt: createdAt,
      updatedAt: updatedAt
    )
  }
}

public final class CollectionDTO: Sendable, Identifiable {
  public let serverId: Int?
  public let name: String?
  public let thumbnail: String?
  public let tracks: [TrackDTO]?
  public let createdAt: Date?
  public let updatedAt: Date?

  public init(
    serverId: Int? = nil,
    name: String? = nil,
    thumbnail: String? = nil,
    tracks: [TrackDTO]? = nil,
    createdAt: Date? = nil,
    updatedAt: Date? = nil
  ) {
    self.serverId = serverId
    self.name = name
    self.thumbnail = thumbnail
    self.tracks = tracks
    self.createdAt = createdAt
    self.updatedAt = updatedAt
  }

  public func toModel() -> Collection {
    Collection(
      serverId: serverId,
      name: name,
      tracks: tracks?.map { $0.toModel() } ?? [],
      thumbnail: thumbnail,
      createdAt: createdAt,
      updatedAt: updatedAt
    )
  }
}
